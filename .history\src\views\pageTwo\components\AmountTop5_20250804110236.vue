<!--
  机构放款金额Top5组件
  功能：
  1. 展示机构放款金额的前5名数据
  2. 提供3D柱状图可视化展示
  3. 显示累计放款金额和笔数
  4. 支持图表交互和提示
-->
<template>
  <div class="AmountTop5-dataAcquisition">
    <!-- 头部标题区域 -->
    <div class="AmountTop5-headerImg">
      <div>机构放款动态</div>
      <div class="ChainDistribution-rightClass">
        <div class="ChainDistribution-djgdClass" @click="handleClick">
          点击查看更多
        </div>
        <div>
          <img src="@/assets/dp/rightT.png" class="ChainDistribution-imgRight" />
        </div>
      </div>
    </div>
    <div class="AmountTop5-bottomClass">
      <!-- 顶部统计信息 -->
      <div class="AmountTop5-top-summary">
        <span>累计放款金额：</span>
        <span class="AmountTop5-highlight">{{ amount || 0 }}</span>亿元
        <span class="AmountTop5-highlight2">{{
          parseInt(count) ? parseInt(count) : 0
        }}</span>笔
      </div>
      <!-- 图表容器 -->
      <div ref="barChart" class="AmountTop5-echart-bar"></div>
    </div>
    <dialog-bar-park2 :visible="dialogVisible" :data="parkList" title="机构放款动态" :display-count="9"
      @close="handleDialogClose" :selectedRegion="selectedRegion" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import DialogBarPark2 from "@/views/components/dialogBar-park2.vue";

export default {
  name: "AmountTop5",
  props: {
    //区域
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  components: { DialogBarPark2 },
  data() {
    return {
      dialogVisible: false,
      // 所有数据
      allBanks: [], // 所有银行名称
      allValues: [], // 所有放款金额数据（单位：亿）
      allCounts: [], // 所有放款笔数数据

      // 当前显示的数据
      currentDisplayBanks: [], // 当前显示的银行名称
      currentDisplayValues: [], // 当前显示的放款金额
      currentDisplayCounts: [], // 当前显示的放款笔数

      amount: 0, // 累计放款金额
      count: 0, // 累计放款笔数
      dialogAutoPlayTimer: null, // 自动播放定时器
      timerIndex: 0, // 当前播放的索引
      addResizeListener: false, // 是否添加了resize监听器
      parkList: [],
      top5List: [], // Top5数据列表
      chart: null, // 图表实例
      scrollTimer: null, // 自动滚动定时器
      currentIndex: 0, // 当前滚动索引
      scrollSpeed: 3000, // 滚动间隔时间（毫秒）
      scrollStep: 1, // 每次滚动的条数
      resizeHandler: null, // resize事件处理器
    };
  },
  // 监听器
  watch: {
    // isMaxDtReady: {
    //   handler(newVal) {
    //     if (newVal) {
    //       // 只有当 maxDt 准备好后才调用其他接口
    //       this.getrzjrjg();
    //     }
    //   },
    //   immediate: true,
    // },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.fetchDataAndInitChart(newVal);
      },
    },
  },
  mounted() {
    // 获取数据并初始化图表
    this.fetchDataAndInitChart();
  },
  methods: {
    // 获取数据并初始化图表
    async fetchDataAndInitChart() {
      try {
        const res = await productUpdateList([
          {
            bizDate: localStorage.getItem("maxDt"),
            dimTime: "ALL",
            dimIndustry: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            dimNum: this.selectedRegion === "ALL" ? 0 : 2,
            dimIndustryChain: "ALL",
            indexName: "累计放款金额",
          },
          {
            bizDate: localStorage.getItem("maxDt"),
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            // dimNum: 1,
            dimNum: this.selectedRegion === "ALL" ? 1 : 2,
            dimIndustryChain: "ALL",
            indexName: "累计放款笔数",
          },
          {
            bizDate: localStorage.getItem("maxDt"),
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            // dimNum: 1,
            dimNum: this.selectedRegion === "ALL" ? 1 : 2,
            dimIndustryChain: "ALL",
            indexName: "金融机构放款金额",
          },
        ]);

        this.amount = res.data.data[0]?.indexValue;
        this.count = res.data.data[1]?.indexValue;
        const dataContent = res?.data?.data[2]?.bizContent;
        if (dataContent) {
          this.top5List = JSON.parse(dataContent);
        }
        this.parkList = this.top5List
          .map((item) => {
            return { ...item, value: item.loanAmt.replace("亿元", "") };
          })
          .sort((a, b) => b.value - a.value);

        console.log("主页面组件生成的parkList数据:", this.parkList);
        console.log("当前选择的区域:", this.selectedRegion);

        // 保存所有数据
        this.allValues = this.top5List.map((item) =>
          Number(item.loanAmt.replace("亿元", ""))
        );
        this.allCounts = this.top5List.map((item) => item.loanSum);
        this.allBanks = this.top5List.map((item) => item.shortName);

        // 初始化当前显示的数据（前5条）
        this.updateCurrentDisplayData(0);

        // 使用 $nextTick 确保DOM渲染完成后再初始化图表
        await this.$nextTick();
        this.initBarChart();

        // 启动自动滚动
        if (this.allBanks.length > 0) {
          this.startAutoScroll(this.allBanks.length);
        }
      } catch (error) {
        console.error("获取数据失败:", error);
      }
    },

    // 更新当前显示的数据
    updateCurrentDisplayData(startIndex) {
      const totalItems = this.allBanks.length;
      this.currentDisplayBanks = [];
      this.currentDisplayValues = [];
      this.currentDisplayCounts = [];

      // 根据实际数据量决定显示数量，最多显示5个
      const displayCount = Math.min(5, totalItems);

      for (let i = 0; i < displayCount; i++) {
        const actualIndex = (startIndex + i) % totalItems;
        this.currentDisplayBanks.push(this.allBanks[actualIndex]);
        this.currentDisplayValues.push(this.allValues[actualIndex]);
        this.currentDisplayCounts.push(this.allCounts[actualIndex]);
      }
    },

    // 启动自动滚动
    startAutoScroll(totalItems) {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }

      // 如果数据少于等于5条，不需要滚动
      if (totalItems <= 5) {
        return;
      }

      this.scrollTimer = setInterval(() => {
        // 更新当前索引
        this.currentIndex = (this.currentIndex + this.scrollStep) % totalItems;

        // 更新当前显示的数据
        this.updateCurrentDisplayData(this.currentIndex);

        let copydisplayValues = this.currentDisplayValues.map((item) => {
          return item * 1.05;
        });

        // 更新图表
        if (this.chart && !this.chart.isDisposed()) {
          this.chart.setOption({
            // 动画配置
            animation: true,
            animationDuration: 1000,
            animationEasing: "cubicOut",
            animationDelay: function (idx) {
              return idx * 100;
            },
            xAxis: {
              data: this.currentDisplayBanks,
            },
            series: [
              // 主柱状图 - 正面
              {
                name: "main",
                data: this.currentDisplayValues,
                animationDelay: function (idx) {
                  return idx * 100;
                },
              },
              // 右侧面 - 深度效果
              {
                name: "right",
                data: copydisplayValues,
                animationDelay: function (idx) {
                  return idx * 100 + 50;
                },
              },
              // 顶面 - 3D顶部效果
              {
                name: "top3d",
                data: this.currentDisplayValues,
                symbolOffset: (() => {
                  // 根据当前显示的柱子数量动态调整偏移量
                  const displayCount = this.currentDisplayBanks.length;
                  if (displayCount <= 2) {
                    // 当柱子数量较少时，使用更大的偏移量来补偿布局偏差
                    return [
                      this.$autoFontSize ? this.$autoFontSize(-17) : -17,
                      this.$autoFontSize ? this.$autoFontSize(-4) : -4,
                    ];
                  } else if (displayCount <= 3) {
                    // 当柱子数量为3个时，使用中等偏移量
                    return [
                      this.$autoFontSize ? this.$autoFontSize(-10) : -10,
                      this.$autoFontSize ? this.$autoFontSize(-5) : -5,
                    ];
                  } else {
                    // 当柱子数量较多时，使用默认偏移量
                    return [
                      this.$autoFontSize ? this.$autoFontSize(-4) : -4,
                      this.$autoFontSize ? this.$autoFontSize(-6) : -6,
                    ];
                  }
                })(),
                animationDelay: function (idx) {
                  return idx * 100 + 100;
                },
              },
              // 标签系列
              {
                data: copydisplayValues,
                animationDelay: function (idx) {
                  return idx * 100 + 200;
                },
              },
            ],
          });
        }
      }, this.scrollSpeed);
    },

    // 初始化3D柱状图
    initBarChart() {
      // 确保DOM元素存在
      if (!this.$refs.barChart) {
        console.error("图表容器DOM元素不存在");
        return;
      }

      // 如果图表已存在，先销毁
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }

      try {
        this.chart = echarts.init(this.$refs.barChart);

        let copydisplayValues = this.currentDisplayValues.map((item) => {
          return item * 1.05;
        });

        // 图表配置项
        const option = {
          // 动画配置
          animation: true,
          animationDuration: 1000,
          animationEasing: "cubicOut",
          animationDelay: function (idx) {
            return idx * 100;
          },
          // 提示框配置
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
            formatter: (params) => {
              const item = params[0];
              const dataIndex = item.dataIndex;
              const value = this.currentDisplayValues[dataIndex];
              const count = this.currentDisplayCounts[dataIndex];

              return (
                '<div style="text-align:center;">' +
                `<span style="color:#ffce7c;font-size:16px;">${value}亿元</span><br/>` +
                `<span style="color:#7cebff;font-size:16px;">${count}</span>` +
                "</div>"
              );
            },
            backgroundColor: "rgba(10,29,56,0.95)",
            borderColor: "#0a4d8f",
            borderWidth: 1,
            extraCssText: "box-shadow:0 0 8px #0a4d8f;",
          },
          // 图表网格配置
          grid: {
            left: "0%",
            right: "0%",
            top: "30%",
            bottom: "2%",
            containLabel: true,
          },
          // X轴配置
          xAxis: {
            type: "category",
            data: this.currentDisplayBanks,
            axisLine: {
              show: true,
              lineStyle: {
                width: 1,
                color: "rgba(255,255,255,0.2)",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#fff",
              fontSize: this.$autoFontSize ? this.$autoFontSize(14) : 14,
              interval: 0,
              formatter: function (value) {
                return value.replace(/(.{4})/g, "\$1\n");
              },
            },
          },
          // Y轴配置
          yAxis: {
            show: false,
          },
          // 系列配置
          series: [
            // 主柱状图 - 正面
            {
              name: "main",
              tooltip: {
                show: false,
              },
              type: "bar",
              barWidth: this.$autoFontSize ? this.$autoFontSize(20) : 20,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    1,
                    0,
                    0,
                    [
                      {
                        offset: 0,
                        color: "rgba(42,205,253, 0.6)", // 底部颜色
                      },
                      {
                        offset: 0.6,
                        color: "rgba(42,205,253, 0.8)", // 中间颜色
                      },
                      {
                        offset: 1,
                        color: "rgba(42,205,253, 1)", // 顶部颜色
                      },
                    ],
                    false
                  ),
                },
              },
              barGap: 0,
              data: this.currentDisplayValues,
              animationDelay: function (idx) {
                return idx * 100;
              },
            },
            // 右侧面 - 深度效果
            {
              name: "right",
              tooltip: {
                show: false,
              },
              type: "bar",
              barWidth: this.$autoFontSize ? this.$autoFontSize(10) : 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    1,
                    0,
                    0,
                    [
                      {
                        offset: 0,
                        color: "rgba(42,205,253, 0.4)", // 底部颜色
                      },
                      {
                        offset: 0.6,
                        color: "rgba(42,205,253, 0.6)", // 中间颜色
                      },
                      {
                        offset: 1,
                        color: "rgba(42,205,253, 0.8)", // 顶部颜色
                      },
                    ],
                    false
                  ),
                },
              },
              barGap: 0,
              data: copydisplayValues,
              animationDelay: function (idx) {
                return idx * 100 + 50;
              },
            },
            // 顶面 - 3D顶部效果
            {
              name: "top3d",
              tooltip: {
                show: false,
              },
              type: "pictorialBar",
              itemStyle: {
                borderWidth: 2,
                borderColor: "#000",
                color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                  {
                    offset: 0,
                    color: "rgba(42,205,253, 1)",
                  },
                  {
                    offset: 1,
                    color: "#419EFF",
                  },
                ]),
              },
              symbol: "path://M 0,0 l 80,0 l -15,10 l -80,0 z",
              symbolSize: [
                this.$autoFontSize ? this.$autoFontSize(32.9) : 32.9,
                this.$autoFontSize ? this.$autoFontSize(9) : 9,
              ],
              symbolOffset: (() => {
                // 根据当前显示的柱子数量动态调整偏移量
                const displayCount = this.currentDisplayBanks.length;
                if (displayCount <= 2) {
                  // 当柱子数量较少时，使用更大的偏移量来补偿布局偏差
                  return [
                    this.$autoFontSize ? this.$autoFontSize(-50) : -50,
                    this.$autoFontSize ? this.$autoFontSize(-10) : -4,
                  ];
                } else if (displayCount <= 3) {
                  // 当柱子数量为3个时，使用中等偏移量
                  return [
                    this.$autoFontSize ? this.$autoFontSize(-10) : -10,
                    this.$autoFontSize ? this.$autoFontSize(-5) : -5,
                  ];
                } else {
                  // 当柱子数量较多时，使用默认偏移量
                  return [
                    this.$autoFontSize ? this.$autoFontSize(-4) : -4,
                    this.$autoFontSize ? this.$autoFontSize(-6) : -6,
                  ];
                }
              })(),
              symbolPosition: "end",
              data: this.currentDisplayValues,
              z: 3,
              animationDelay: function (idx) {
                return idx * 100 + 100;
              },
            },
            // 标签系列
            {
              type: "bar",
              label: {
                show: true,
                position: "top",
                padding: [
                  0,
                  0,
                  this.$autoFontSize ? this.$autoFontSize(5) : 5,
                  0,
                ],
                formatter: (params) => {
                  const value = this.currentDisplayValues[params.dataIndex];
                  const count = this.currentDisplayCounts[params.dataIndex];
                  return `{gold|${value}亿元}\n{blue|${count}}`;
                },
                rich: {
                  gold: {
                    color: "#ffce7c",
                    fontSize: this.$autoFontSize ? this.$autoFontSize(14) : 14,
                    padding: [
                      0,
                      this.$autoFontSize ? this.$autoFontSize(28) : 28,
                      this.$autoFontSize ? this.$autoFontSize(5) : 5,
                      0,
                    ],
                    align: "center",
                  },
                  blue: {
                    color: "#7cebff",
                    padding: [
                      0,
                      this.$autoFontSize ? this.$autoFontSize(28) : 28,
                      0,
                      0,
                    ],
                    fontSize: this.$autoFontSize ? this.$autoFontSize(14) : 14,
                    align: "center",
                  },
                },
              },
              itemStyle: {
                color: "transparent",
              },
              tooltip: {},
              data: copydisplayValues,
              animationDelay: function (idx) {
                return idx * 100 + 200;
              },
            },
          ],
        };

        this.chart.setOption(option);

        // 添加resize监听器
        this.resizeHandler = () => {
          if (this.chart && !this.chart.isDisposed()) {
            this.chart.resize();
          }
        };
        window.addEventListener("resize", this.resizeHandler);
        this.addResizeListener = true;
      } catch (error) {
        console.error("初始化图表失败:", error);
      }
    },

    handleClick() {
      this.dialogVisible = true;
    },

    handleDialogClose() {
      this.dialogVisible = false;
    },
  },

  beforeDestroy() {
    // 组件销毁前清除定时器和事件监听器
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
      this.scrollTimer = null;
    }

    if (this.dialogAutoPlayTimer) {
      clearTimeout(this.dialogAutoPlayTimer);
      this.dialogAutoPlayTimer = null;
    }

    // 移除resize监听器
    if (this.addResizeListener && this.resizeHandler) {
      window.removeEventListener("resize", this.resizeHandler);
    }

    // 销毁图表实例
    if (this.chart && !this.chart.isDisposed()) {
      this.chart.dispose();
      this.chart = null;
    }
  },
};
</script>

<style scoped lang="scss">
/* 主容器样式 */
.AmountTop5-dataAcquisition {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 头部标题样式 */
  .AmountTop5-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;

    .ChainDistribution-rightClass {
      display: flex;
      align-items: center;
      justify-content: center;

      .ChainDistribution-djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }

      .ChainDistribution-imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  /* 底部内容区域样式 */
  .AmountTop5-bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(180deg,
        rgba(3, 29, 58, 0) 0%,
        rgba(0, 50, 107, 0.64) 100%);
    border: 1px solid;
    border-top: 0;
    border-image: linear-gradient(169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)) 2 2;
    backdrop-filter: blur(7px);
    display: flex;
    flex-direction: column;
    align-items: center;

    /* 顶部统计信息样式 */
    .AmountTop5-top-summary {
      width: 940px;
      text-align: left;
      font-size: 26px;
      color: #fff;
      margin-left: 100px;

      /* 金额高亮样式 */
      .AmountTop5-highlight {
        background: linear-gradient(180deg, #fff 0%, #ffce7c 100%);
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 44px;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      /* 笔数高亮样式 */
      .AmountTop5-highlight2 {
        margin-left: 20px;
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 44px;
        background: linear-gradient(180deg, #fff 0%, #7cebff 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    /* 图表容器样式 */
    .AmountTop5-echart-bar {
      width: 100%;
      height: 19vh;
      margin-top: 0;
    }
  }
}
</style>
